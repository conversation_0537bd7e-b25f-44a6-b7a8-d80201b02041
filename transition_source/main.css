* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  font-family: "Neue Haas Grotesk Display Pro";
  background-color: #000;
}

img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.container {
  width: 100vw;
  height: 100%;
  min-height: 100vh;
  background-color: #f1efe7;
}

nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  padding: 1.5em;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.link {
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
}

nav a {
  display: inline-block;
  position: relative;
  transform: translateY(16px);
  will-change: transform;
  text-decoration: none;
  color: #242726;
  font-size: 14px;
  font-weight: 600;
}

.links {
  display: flex;
  gap: 1em;
}

.hero h1 {
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-transform: uppercase;
  color: #242726;
  font-size: 20vw;
  font-weight: bolder;
  display: flex;
  justify-content: center;
  letter-spacing: -0.5rem;
  line-height: 1;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
}

.hero h1 .char {
  position: relative;
  will-change: transform;
}

.images {
  width: 100%;
  background-color: #f1efe7;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1em;
  padding: 15em 0;
}

.images img {
  width: 35%;
  margin: 0 auto;
}

.info {
  width: 100%;
  height: 100%;
  display: flex;
}

.col {
  flex: 1;
}

.col:nth-child(2) {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2em;
}

.col p {
  font-weight: 600;
  font-size: 2rem;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #242726;
}

.col p .line {
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
}

.col p .line span {
  position: relative;
  will-change: transform;
}

/* transition */
::view-transition-old(root),
::view-transition-new(root) {
  animation-duration: 0.5s;
}

@keyframes move-out {
  from {
    opacity: 1;
    transform: translateY(0);
  }

  to {
    opacity: 0.4;
    transform: translateY(-35%);
  }
}

@keyframes move-in {
  from {
    clip-path: polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%);
  }

  to {
    clip-path: polygon(0% 100%, 100% 100%, 100% 0%, 0% 0%);
  }
}

::view-transition-old(root) {
  animation: 1.5s cubic-bezier(0.87, 0, 0.13, 1) both move-out;
}

::view-transition-new(root) {
  animation: 1.5s cubic-bezier(0.87, 0, 0.13, 1) both move-in;
}
