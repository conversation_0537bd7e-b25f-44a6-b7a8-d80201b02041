# Structure des Fichiers Vidéo

## 📁 Organisation des Fichiers

Pour utiliser le système responsive, organisez vos vidéos comme suit :

```
public/videos/
├── copilote-cards-480p.webm     # Version mobile
├── copilote-cards-480p.mp4      # Fallback mobile
├── copilote-cards-720p.webm     # Version desktop
└── copilote-cards-720p.mp4      # Fallback desktop
```

## 🎯 Convention de Nommage

**Format** : `nom-video-{résolution}.{extension}`

- `nom-video` : Nom de base de votre vidéo
- `résolution` : `480p` pour mobile, `720p` pour desktop
- `extension` : `webm` (principal) et `mp4` (fallback)

## 📱 Résolutions Recommandées

### Mobile (480p)
- **Résolution** : 854×480 ou 640×480
- **Bitrate** : 500-800 kbps
- **Usage** : Écrans ≤768px

### Desktop (720p)
- **Résolution** : 1280×720
- **Bitrate** : 1000-1500 kbps
- **Usage** : Écrans >768px

## 🔧 Configuration dans projectsData.js

### Simple (recommandé)
```javascript
{
  id: "copilote",
  src: "videos/copilote-cards", // Sans extension ni résolution
  mediaType: "video"
  // Le système ajoute automatiquement -480p/-720p et .webm/.mp4
}
```

### Personnalisée
```javascript
{
  id: "mon-projet",
  src: "videos/ma-video",
  mediaType: "video",
  videoSources: {
    mobile: { maxWidth: 600, quality: '360p' },
    tablet: { maxWidth: 1200, quality: '720p' },
    desktop: { maxWidth: 9999, quality: '1080p' }
  }
}
```

## 🚀 Fonctionnement Automatique

1. **Détection d'écran** : Le système détecte la largeur de l'écran
2. **Sélection automatique** :
   - ≤768px → `nom-video-480p.webm`
   - >768px → `nom-video-720p.webm`
3. **Fallback** : Si WebM échoue → `nom-video-480p.mp4`

## 📋 Checklist pour Nouveau Projet

- [ ] Créer `nom-video-480p.webm`
- [ ] Créer `nom-video-480p.mp4`
- [ ] Créer `nom-video-720p.webm`
- [ ] Créer `nom-video-720p.mp4`
- [ ] Ajouter dans projectsData.js :
  ```javascript
  {
    src: "videos/nom-video",
    mediaType: "video"
  }
  ```

## 🎬 Exemple Complet - Projet Copilote

### Fichiers requis :
```
public/videos/
├── copilote-cards-480p.webm
├── copilote-cards-480p.mp4
├── copilote-cards-720p.webm
└── copilote-cards-720p.mp4
```

### Configuration :
```javascript
{
  id: "copilote",
  client: "Copilote",
  src: "videos/copilote-cards",
  mediaType: "video"
}
```

### Résultat :
- 📱 Mobile : Charge automatiquement `copilote-cards-480p.webm`
- 💻 Desktop : Charge automatiquement `copilote-cards-720p.webm`
- 🔄 Adaptation temps réel au resize de fenêtre

## ⚡ Avantages

- **Simplicité** : Juste 4 fichiers par vidéo
- **Performance** : Vidéos adaptées à chaque écran
- **Automatique** : Aucune configuration complexe
- **Flexible** : Personnalisable si besoin
