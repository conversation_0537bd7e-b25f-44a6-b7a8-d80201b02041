import { useState, useEffect } from 'react';

/**
 * Hook pour gérer les vidéos responsives
 * Similaire au système de Next.js Image mais pour les vidéos
 */
export const useResponsiveVideo = (baseSrc, customBreakpoints = {}) => {
  const [currentSrc, setCurrentSrc] = useState('');
  const [quality, setQuality] = useState('desktop');

  // Breakpoints par défaut pour 480, 720, 1080
  const defaultBreakpoints = {
    mobile: { maxWidth: 768, quality: '480', suffix: 'sm' },
    tablet: { maxWidth: 1200, quality: '720', suffix: 'md' },
    desktop: { maxWidth: 9999, quality: '1080', suffix: 'lg' }
  };

  const breakpoints = { ...defaultBreakpoints, ...customBreakpoints };

  // Détermine la meilleure qualité selon la taille d'écran
  const getBestQuality = () => {
    if (typeof window === 'undefined') return 'desktop';
    
    const screenWidth = window.innerWidth;
    const devicePixelRatio = window.devicePixelRatio || 1;
    
    // Ajuste selon la densité de pixels (écrans Retina)
    const effectiveWidth = screenWidth * devicePixelRatio;
    
    for (const [key, config] of Object.entries(breakpoints)) {
      if (effectiveWidth <= config.maxWidth) {
        return config.quality;
      }
    }
    
    return '1080';
  };

  // Génère l'URL de la vidéo selon la qualité
  const generateVideoSrc = (quality, format = 'webm') => {
    const extension = baseSrc.split('.').pop();
    const baseWithoutExt = baseSrc.replace(`.${extension}`, '');
    return `${baseWithoutExt}-${quality}.${format}`;
  };

  // Met à jour la source vidéo
  useEffect(() => {
    const updateVideoSrc = () => {
      const bestQuality = getBestQuality();
      setQuality(bestQuality);
      setCurrentSrc(generateVideoSrc(bestQuality));
    };

    updateVideoSrc();
    
    // Écoute les changements de taille d'écran
    const handleResize = () => {
      updateVideoSrc();
    };

    window.addEventListener('resize', handleResize);
    
    return () => window.removeEventListener('resize', handleResize);
  }, [baseSrc]);

  return {
    currentSrc,
    quality,
    generateVideoSrc,
    breakpoints
  };
};

/**
 * Génère les sources multiples pour l'élément <video>
 */
export const generateVideoSources = (baseSrc, formats = ['webm', 'mp4']) => {
  const breakpoints = {
    mobile: { maxWidth: 768, quality: '480' },
    tablet: { maxWidth: 1200, quality: '720' },
    desktop: { maxWidth: 9999, quality: '1080' }
  };

  const sources = [];
  
  Object.entries(breakpoints).forEach(([key, config]) => {
    formats.forEach(format => {
      const extension = baseSrc.split('.').pop();
      const baseWithoutExt = baseSrc.replace(`.${extension}`, '');
      const src = `${baseWithoutExt}-${config.quality}.${format}`;
      
      sources.push({
        src,
        type: `video/${format}`,
        media: `(max-width: ${config.maxWidth}px)`,
        quality: config.quality
      });
    });
  });

  return sources;
};

/**
 * Précharge les vidéos pour une meilleure performance
 */
export const preloadVideo = (src, priority = 'low') => {
  if (typeof window === 'undefined') return;
  
  const video = document.createElement('video');
  video.preload = priority === 'high' ? 'auto' : 'metadata';
  video.src = src;
  
  // Optionnel : ajouter au DOM de manière invisible pour forcer le préchargement
  video.style.display = 'none';
  document.body.appendChild(video);
  
  // Nettoie après chargement
  video.addEventListener('loadedmetadata', () => {
    document.body.removeChild(video);
  });
};

export default useResponsiveVideo;
