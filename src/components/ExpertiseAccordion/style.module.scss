@import "@/styles/colors.scss";

.expertiseAccordion {
    display: flex;
    flex-direction: column;
    padding-top: var(--section-padding);
    padding-bottom: var(--section-padding);
    justify-content: center;
}

.body {
    display: flex;
    flex-direction: column;
    gap: var(--gap-padding);
    width: 100%;
}

.titleSection {
        margin-top: calc(var(--gap-padding) / 1.5);
}

.mask {
    overflow: hidden;
    display: inline-block;
    margin-right: 0.3em;
}

.accordionSection {
    width: 100%;
}

.accordionWrapper {
    // Styles pour le composant Accordion réutilisé
    .wrapper {
        border-bottom: 1px solid $color-light-gray;

        &:first-child {
            border-top: 1px solid $color-light-gray;
        }
    }
}

// Ajustements pour tablettes et desktops
@media (min-width: 768px) {
    .expertiseAccordion {
        align-items: stretch;
    }

    .body {
        display: grid;
        grid-template-columns: 1fr 1.5fr; // 40% / 60% ratio
        gap: var(--gap-padding);
        align-items: start;
        width: 100%;
    }
    
    // Les styles sont maintenant gérés par le composant Accordion
}
