"use client";

import styles from './style.module.scss';
import { useInView, motion } from 'framer-motion';
import { useRef, useMemo } from 'react';
import { slideUp, opacity } from './animation';
import { Accordion, AccordionItem } from '@/components/AccordionComponent/Accordion';
import CursorFollower, { useCursorFollower } from '@/components/CursorFollower';
import { useTranslation } from '@/hooks/useTranslation';

export default function ExpertiseAccordion({
    namespace = 'agency',
    title = 'expertises.title',
    expertisesData = 'expertises.items',
    cursorText = 'expertises.cursor_text'
}) {
    const { t } = useTranslation(namespace);
    const container = useRef(null);
    const isInView = useInView(container, { once: true });

    // Hook pour gérer la bulle qui suit la souris
    const { isVisible, showCursor, hideCursor, updatePosition, cursorRef } = useCursorFollower();

    // Récupérer les données d'expertises depuis les traductions
    const expertisesList = useMemo(() => {
        const expertises = [];
        let index = 0;

        // Boucle pour récupérer toutes les expertises disponibles
        while (true) {
            const expertiseTitle = t(`${expertisesData}.${index}.title`);
            const expertiseDetails = t(`${expertisesData}.${index}.details`);

            // Si la traduction retourne la clé au lieu de la valeur, on arrête
            if (expertiseTitle === `${expertisesData}.${index}.title` ||
                expertiseDetails === `${expertisesData}.${index}.details`) {
                break;
            }

            expertises.push({
                title: expertiseTitle,
                items: Array.isArray(expertiseDetails) ? expertiseDetails : []
            });
            index++;
        }

        return expertises;
    }, [t, expertisesData]);

    // Gestionnaire de survol pour l'accordéon
    const handleMouseEnter = (e) => {
        showCursor(e.clientX, e.clientY);
    };

    const handleMouseMove = (e) => {
        updatePosition(e.clientX, e.clientY);
    };

    const handleMouseLeave = () => {
        hideCursor();
    };

    return (
        <div ref={container} className={`${styles.expertiseAccordion} container`}>
            <div className={styles.body}>
                {/* Titre à gauche */}
                    <h2 className={styles.titleSection}>
                     {t(title)}
                    </h2>

                {/* Accordéon à droite */}
                <motion.div
                    variants={opacity}
                    animate={isInView ? "open" : "closed"}
                    className={styles.accordionSection}
                    onMouseEnter={handleMouseEnter}
                    onMouseMove={handleMouseMove}
                    onMouseLeave={handleMouseLeave}
                >
                    <div className={styles.accordionWrapper}>
                        <Accordion>
                            {expertisesList.map((expertise, index) => (
                                <AccordionItem
                                    key={index}
                                    hideIcon={true}
                                    title={expertise.title}
                                    items={expertise.items || []}
                                />
                            ))}
                        </Accordion>
                    </div>
                </motion.div>

                {/* Bulle qui suit la souris */}
                <CursorFollower
                    ref={cursorRef}
                    text={t(cursorText)}
                    backgroundColor="#FF413D"
                    textColor="white"
                    size={80}
                    fontSize={14}
                    fontWeight={300}
                    isVisible={isVisible}
                />
            </div>
        </div>
    );
}
