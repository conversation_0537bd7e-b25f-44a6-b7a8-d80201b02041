"use client";

import React, { useRef } from "react";
import gsap from "gsap";
import { useGSAP } from "@gsap/react";

// Note: SplitText is a premium GSAP plugin
// For now, we'll create a basic version that can be upgraded later
const createSplitText = (element) => {
  const text = element.textContent;
  const chars = text.split('').map((char, index) => {
    const span = document.createElement('span');
    span.textContent = char === ' ' ? '\u00A0' : char; // Non-breaking space
    span.className = 'char';
    span.style.display = 'inline-block';
    span.style.position = 'relative';
    span.style.overflow = 'hidden';
    return span;
  });
  
  element.innerHTML = '';
  chars.forEach(char => element.appendChild(char));
  
  return {
    chars: chars,
    revert: () => {
      element.textContent = text;
    }
  };
};

export default function TransitionCopy({ children, animateOnScroll = true, delay = 0 }) {
  const containerRef = useRef(null);
  const elementRefs = useRef([]);
  const splitRefs = useRef([]);
  const chars = useRef([]);

  useGSAP(
    () => {
      if (!containerRef.current) return;

      splitRefs.current = [];
      chars.current = [];
      elementRefs.current = [];

      let elements = [];
      if (containerRef.current.hasAttribute("data-copy-wrapper")) {
        elements = Array.from(containerRef.current.children);
      } else {
        elements = [containerRef.current];
      }

      elements.forEach((element) => {
        elementRefs.current.push(element);

        // Use our basic split text implementation
        const split = createSplitText(element);
        
        splitRefs.current.push(split);
        chars.current.push(...split.chars);
      });

      gsap.set(chars.current, { y: "100%" });

      const animationProps = {
        y: "0%",
        duration: 1,
        stagger: 0.03,
        ease: "power4.out",
        delay: delay,
      };

      if (animateOnScroll) {
        // Basic scroll trigger without the plugin for now
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              gsap.to(chars.current, animationProps);
              observer.unobserve(entry.target);
            }
          });
        }, { threshold: 0.1 });

        if (containerRef.current) {
          observer.observe(containerRef.current);
        }

        return () => {
          observer.disconnect();
        };
      } else {
        gsap.to(chars.current, animationProps);
      }

      return () => {
        splitRefs.current.forEach((split) => {
          if (split && split.revert) {
            split.revert();
          }
        });
      };
    },
    { scope: containerRef, dependencies: [animateOnScroll, delay] }
  );

  if (React.Children.count(children) === 1) {
    return React.cloneElement(children, { ref: containerRef });
  }

  return (
    <div ref={containerRef} data-copy-wrapper="true">
      {children}
    </div>
  );
}
