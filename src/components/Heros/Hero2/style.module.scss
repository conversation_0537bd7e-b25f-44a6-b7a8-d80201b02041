@import "@/styles/colors.scss";

/* ─────────── conteneur principal ─────────── */
.root {
  display: flex;
  flex-direction: column;
  gap: clamp(3rem, 6vw, 8rem);
  padding-top: calc(var(--section-padding) / 2);
  padding-bottom: calc(var(--section-padding) / 2);
}

/* ─────────── bloc titres ─────────── */
.header {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.title {
  margin: 0 0 1rem 0;
}

.title, .subtitles, .text {
  color: $color-dark-brown;
}

/* group sous‑titres */
.subtitles {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 1.5rem;
}

/* sous‑titre gauche */
.subtitle {
  margin: 0;
  font-size: clamp(1.125rem, 1.5vw + 0.5rem, 1.5rem);
  line-height: 1.4;
}

/* sous‑titre droite + ic<PERSON> */
.subtitleRight {
  margin: 0;
  display: inline-flex;
  align-items: flex-end;
  gap: 0.5rem;
  font-size: clamp(1.125rem, 1.5vw + 0.5rem, 1.5rem);
  line-height: 1.4;
  text-align: right;
}

.subtitleIcon {
  width: 0.875rem;  /* 14 px */
  height: 2rem;     /* 16 px */
  flex-shrink: 0;
}

/* ─────────── bloc texte + image ─────────── */
.content {
  display: flex;
  gap: clamp(2rem, 4vw, 4rem);
}

.text {
  flex: 1 1 35rem;        /* ≈ 40 % */
  min-width: 18rem;
}

/* Ratio fixe : 4 : 3 — sans forcer la hauteur en colonne */
.imageContainer {
  position: relative;
  flex: 0 1 50rem;        /* basis desktop, mais pas de hauteur imposée en colonne */
  aspect-ratio: 4 / 3;
  width: 100%;            /* s’étire plein écran si besoin */
}

.image {
  width: 100%;
  height: 100%;           /* remplit le conteneur fixé par aspect‑ratio */
  object-fit: cover;
  clip-path: polygon(24% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 11%);
}

/* ─────────── responsive ─────────── */
@media (max-width: 768px) {
  .subtitles {
    flex-direction: column;
  }

  .subtitleRight {
    text-align: left;
  }

  .content {
    flex-direction: column-reverse;  /* image au‑dessus */
  }

  /* garde le même ratio, hauteur n’est plus 100 % */
  .imageContainer {
    flex: 0 0 auto;   /* annule le flex‑basis vertical */
  }

  .image {
    clip-path: polygon(16% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 20%);
  }
}

@media (min-width: 768px) {
  .image {
    clip-path: polygon(16% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 20%);
  }

  .title {
    max-width: 70%;
  }

  .content {
      align-items: flex-end;
      justify-content: space-between;
  }
}

/* 40 % desktop, pleine largeur mobile */
.textBlock {
  flex: 0 0 40%;     /* basis = 40 %, grow/shrink = 0 */
  max-width: 40%;
}

@media (max-width: 768px) {
  .textBlock {
    flex: 1 1 auto;  /* reprend le flux normal en colonne */
    max-width: none;
  }
}