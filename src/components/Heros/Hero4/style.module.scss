@import "@/styles/colors.scss";

.content {
  display: flex;
  gap: 4rem;
  align-items: flex-end;
  justify-content: end;

  @media (max-width: 768px) {
    flex-direction: column-reverse;
    gap: 2rem;
    align-items: stretch;
  }
}

.textBlock {
  max-width: 430px;
  color: $color-dark-brown;

  @media (max-width: 768px) {
    flex: 1;
    max-width: 100%;
  }
}

.imageContainer {
  flex: 0 0 50%;
  position: relative;
  aspect-ratio: 4/3;

  @media (max-width: 768px) {
    flex: 1;
    aspect-ratio: 16/10;
  }
}

.imageWrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* ─────────── Responsive adjustments ─────────── */

@media (min-width: 769px) and (max-width: 1024px) {
  .hero {
    padding: 3rem 0 5rem;
  }
  
  .content {
    gap: 3rem;
  }
  
  .imageContainer {
    flex: 0 0 45%;
  }
}

.main {
  padding-bottom: var(--section-padding);
}