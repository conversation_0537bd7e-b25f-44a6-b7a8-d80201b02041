@import "@/styles/colors.scss";

.style0 {
    padding-left: 192.32px;
    padding-right: 192.32px;
    margin: 0 auto;
    max-width: 1600px;
    box-sizing: border-box;
  }
  
  .style1 {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    position: relative;
  }

.heading {
  margin: 0;
}

.subheading, .heading {
    color: $color-dark-brown;
}

@media (min-width:1200px) {
  .heading {
    max-width: 70%;
  }
}
  
  // .style2 {
  //   transform: translate(0px, 0vh);
  //   will-change: transform;
  //   order: 2;
  //   display: block;
  //   box-sizing: border-box;
  //   position: relative;
  //   width: 100%;
  // }
  
  // .style3 {
  //   // font-size: 73.6225px;
  //   // font-family: "Gregg Sans", sans-serif;
  //   // font-style: normal;
  //   margin-bottom: 0;
  //   // font-weight: 450;
  //   padding: 0;
  //   margin: 0;
  //   // letter-spacing: normal;
  //   // box-sizing: border-box;
  //   // line-height: 78.408px;
  // }
  
  // .style4,
  // .style5 {
  //   display: block;
  //   padding: 0;
  //   margin: 0;
  //   font-style: normal;
  //   font-weight: 450;
  //   letter-spacing: normal;
  //   box-sizing: border-box;

  // }
  