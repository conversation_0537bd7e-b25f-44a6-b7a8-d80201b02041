import GSAPTextReveal from '@/components/GSAPTextReveal'
import { getPreset } from '@/components/GSAPTextReveal/presets'
import styles from './style.module.scss'
import { useTranslation } from '@/hooks/useTranslation'

export default function Hero({
  title,
  subtitle,
  locale = 'fr',
  containerClass = 'container'
}) {
  const { t } = useTranslation('home');

  const defaultTitle = t('hero_title');

  return (
    <main className={`${containerClass} default-hero`}>
      <GSAPTextReveal
        as="h1"
        className={styles.heading}
        {...getPreset('hero')}
      >
        {title || defaultTitle}
      </GSAPTextReveal>
      {subtitle && (
        <GSAPTextReveal
          as="p"
          className={`${styles.subheading} text-big` }
          {...getPreset('lines', { delay: 0.8, stagger: 0.2 })}
        >
          {subtitle}
        </GSAPTextReveal>
      )}
    </main>
  )
}


