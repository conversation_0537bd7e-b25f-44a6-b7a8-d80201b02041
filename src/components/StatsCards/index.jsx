"use client";

import { motion } from 'framer-motion';
import GSAPTextReveal from '@/components/GSAPTextReveal';
import { getPreset } from '@/components/GSAPTextReveal/presets';
import styles from './style.module.scss';
import Separator from '@/components/Separator';


export default function StatsCards({
  statsData = [
    {
      number: "120",
      label: "Nombreux projets réalisés",
      id: 'projects'
    },
    {
      number: "5*",
      label: "Clients satisfaits 5/5 sur Google",
      id: 'clients'
    },
    {
      number: "7",
      label: "Projets dans plusieurs pays",
      id: 'countries'
    }
  ]
}) {



  return (
    <section className={styles.statsSection}>
      <div className="container">
        <div className={styles.statsGrid}>
          {statsData.map((stat, index) => (
            <div key={stat.id} className={styles.statCard}>
              <GSAPTextReveal
                as="p"
                className="text-big"
                {...getPreset('lines', { delay: 0.5 + (index * 0.15), stagger: 0.1 })}
              >
                {stat.label}
              </GSAPTextReveal>
              <Separator
                animated={true}
                className={styles.separator}
              />
              <div
                className={`${styles.maskWrapper} ${styles.statCardBg} ${styles[`statCard${stat.id.charAt(0).toUpperCase() + stat.id.slice(1)}`]}`}
                style={{
                  clipPath: "polygon(18% 0%, 100% 0%, 100% 92%, 100% 100%, 0% 100%, 0% 23%)"
                }}
              >
                <div className={styles.cardContent}>
                  <motion.div
                    className={styles.number}
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: true, margin: "-50px" }}
                    transition={{ duration: 0.8, ease: 'easeInOut', delay: (index + 0.3) * 0.15 }}
                  >
                    {stat.number}
                  </motion.div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
