.image {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
    /* Version mobile : la partie supérieure jusqu'à 40% reste intacte */
    clip-path: polygon(18% 0%, 100% 0%, 100% 92%, 92% 100%, 0% 100%, 0% 23%);
  }

  /* Styles spécifiques pour les vidéos */
  .image video {
    width: 100%;
    height: auto;
    max-width: 100%;
    display: block;
    object-fit: cover;
    /* Applique le même clip-path que les images */
    clip-path: polygon(18% 0%, 100% 0%, 100% 92%, 92% 100%, 0% 100%, 0% 23%);
  }
  
  @media (min-width: 768px) {
    .image {
      clip-path: polygon(18% 0%, 100% 0%, 100% 92%, 92% 100%, 0% 100%, 0% 23%);
    }

    .image video {
      clip-path: polygon(18% 0%, 100% 0%, 100% 92%, 92% 100%, 0% 100%, 0% 23%);
    }
  }
  
  @media (min-width: 1024px) {
    .image {
      clip-path: polygon(5% 0%, 100% 0%, 100% 88%, 95% 100%, 0% 100%, 0% 12%);
    }

    .image video {
      clip-path: polygon(5% 0%, 100% 0%, 100% 88%, 95% 100%, 0% 100%, 0% 12%);
    }
  }
  
  
  
  /* Exemple de style pour le bouton, inchangé */
  .button {
    top: 80%;
    left: calc(100% - 200px);
    width: 180px;
    height: 180px;
    background-color: #1C1D20;
    color: white;
    border-radius: 50%;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 300;
      position: relative;
      z-index: 1;
    }
  }

  // .buttonContainer {
  //   z-index: 3;
  // }

  // .buttonLink {
  //   text-decoration: none;
  //   color: inherit;
  //   cursor: default;
  //   text-align: center;
  //   display: flex;
  // }
  
  .buttonLink {
    text-decoration: none;
    color: inherit;
    cursor: default;
    text-align: center;
    display: flex;
    align-items: center; /* aligne verticalement le texte et l'icône */
    gap: 0.25rem;       /* espace entre le label et l'icône */
    position: relative; /* permet à z-index d'agir */
  }
  
  .material-symbols-outlined {
    /* applique les réglages de l'icône si besoin */
    font-variation-settings: 
      'FILL' 0, 
      'wght' 400, 
      'GRAD' 0, 
      'opsz' 24;
    /* Si tu souhaites utiliser z-index sur l'icône, positionne-la aussi en relative */
    position: relative;
    z-index: 1;
  }

  .container {
    position: relative;
  }

.buttonContainer {
  position: relative;
  z-index: 2;
}