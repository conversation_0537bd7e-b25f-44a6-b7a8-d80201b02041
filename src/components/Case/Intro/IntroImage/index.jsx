import MediaDisplay from '@/components/MediaDisplay';
import styles from './style.module.scss';
import { useRef } from 'react';
import { useScroll, motion, useTransform } from 'framer-motion';

const IntroImage = ({ project }) => {
  const container = useRef(null);
  const { scrollYProgress } = useScroll({
    target: container,
    offset: ["start end", "end end"]
  });
  const x = useTransform(scrollYProgress, [0, 1], [0, 100]);

  return (
    <div className="container">
      {/* {project.showButton && project.buttonLink && (
        <div data-scroll data-scroll-speed={0.1} className={styles.buttonContainer}>
          <Rounded className={styles.button}>
            <Link
              href={project.buttonLink}
              target="_blank"
              rel="noopener noreferrer"
              className={styles.buttonLink}
            >
              {project.buttonLabel}
            </Link>
          </Rounded>

        </div>
      )} */}

{(project.srcVideo || project.src) && (
  <MediaDisplay
    src={project.srcVideo || project.src}
    mediaType={project.srcVideo ? 'video' : project.mediaType}
    responsive={project.responsive}
    videoSources={project.videoSources}
    width={1100}
    height={600}
    alt={project.title}
    className={styles.image}
  />
)}

    </div>
  );
};

export default IntroImage;
