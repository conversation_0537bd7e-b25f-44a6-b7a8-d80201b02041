'use client';
import { useRef, useEffect } from 'react';
import styles from './style.module.scss';

export default function PageContainer({ children }) {
  const containerRef = useRef(null);

  useEffect(() => {
    // Exposer la référence globalement pour que le Header puisse l'utiliser
    if (typeof window !== 'undefined') {
      window.pageContainerRef = containerRef;
    }
  }, []);

  return (
    <div ref={containerRef} className={styles.container} id="page-container">
      {children}
    </div>
  );
}
