'use client';
import { useEffect, useRef, useState, useContext } from 'react';

import { useTranslation } from '@/hooks/useTranslation';
import { LenisScrollContext } from '@/components/LocomotiveScrollProvider';
import gsap from 'gsap';
import styles from './style.module.scss';
import AnimatedLink from '@/components/AnimatedLink';
import { useRouter } from 'next/navigation';

export default function MenuOverlay({ 
  isOpen, 
  locale, 
  isAnimating, 
  setIsAnimating, 
  menuToggleLabelRef, 
  hamburgerIconRef, 
  containerRef 
}) {
  const { t } = useTranslation('navigation');
  const lenisScroll = useContext(LenisScrollContext);
  const router = useRouter();
  const menuOverlayRef = useRef(null);
  const menuOverlayContentRef = useRef(null);
  const menuMediaWrapperRef = useRef(null);
  const copyContainersRef = useRef([]);
  const splitTextByContainer = useRef([]);
  const [SplitText, setSplitText] = useState(null);
  const [montrealTime, setMontrealTime] = useState('');
  const [activeImage, setActiveImage] = useState('/images/lucas-joliveau-siege-ordinateur-portable.png');
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [nextImage, setNextImage] = useState('');

  useEffect(() => {
    const timer = setInterval(() => {
      const time = new Date().toLocaleTimeString('en-US', {
        timeZone: 'America/Montreal',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      });
      setMontrealTime(time);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Fonction pour gérer la transition fluide entre les images
  const handleImageTransition = (newImage) => {
    if (newImage === activeImage) return;

    setIsTransitioning(true);
    setNextImage(newImage);

    // Utiliser GSAP pour une transition fluide
    const currentImg = menuMediaWrapperRef.current?.querySelector('img');
    if (currentImg) {
      gsap.to(currentImg, {
        opacity: 0,
        duration: 0.3,
        ease: "power2.out",
        onComplete: () => {
          setActiveImage(newImage);
          gsap.to(currentImg, {
            opacity: 1,
            duration: 0.3,
            ease: "power2.out",
            onComplete: () => {
              setIsTransitioning(false);
              setNextImage('');
            }
          });
        }
      });
    }
  };

  const navItems = [
    {
      title: t('projects'),
      href: `/${locale}/`,
      image: '/images/lucas-joliveau-bibliotheque-macbook.png'
    },
    {
       title: t('agency'),
      href: `/${locale}/agency`,
      image: '/images/lucas-joliveau-google-meet.png'
    },
    {
      title: t('blog'),
      href: `/${locale}/blog`,
      image: '/images/mains-lucas-joliveau-ordinateur-portable.png'
    },
    {
      title: t('contact'),
      href: `/${locale}/contact`,
      image: '/images/lucas-joliveau-telephone-assis.png'
    },
  ];

  const tagItems = [
    { title: t('services.identity_creation'), href: `/${locale}/agency` },
    { title: t('services.strategy_design'), href: `/${locale}/agency` },
    { title: t('services.web_development'), href: `/${locale}/agency` },
  ];

  // Charger SplitText dynamiquement
  useEffect(() => {
    if (typeof window !== 'undefined' && !SplitText) {
      import('gsap/SplitText').then((module) => {
        console.log('SplitText loaded:', module); // Debug
        const SplitTextClass = module.SplitText || module.default;
        setSplitText(() => SplitTextClass);
        gsap.registerPlugin(SplitTextClass);
      }).catch((error) => {
        console.error('Error loading SplitText:', error);
      });
    }
  }, [SplitText]);

  useEffect(() => {
    if (!menuOverlayRef.current || !SplitText) {
      console.log('SplitText setup skipped:', { menuOverlay: !!menuOverlayRef.current, SplitText: !!SplitText });
      return;
    }

    console.log('Setting up SplitText animations...');
    const textContainers = copyContainersRef.current;
    splitTextByContainer.current = [];

    textContainers.forEach((container, containerIndex) => {
      if (!container) {
        console.log(`Container ${containerIndex} is null`);
        return;
      }
      const textElements = container.querySelectorAll("a, p, button");
      console.log(`Container ${containerIndex} found ${textElements.length} text elements`);
      let containerSplits = [];

      textElements.forEach((element, elementIndex) => {
        console.log(`Processing element ${elementIndex}:`, element.textContent);
        const split = new SplitText(element, {
          type: "lines",
          mask: "lines",
          linesClass: "line",
        });
        containerSplits.push(split);
        gsap.set(split.lines, { y: "-110%" });
      });

      splitTextByContainer.current.push(containerSplits);
    });
    console.log('SplitText setup complete');
  }, [SplitText]);

  useEffect(() => {
    if (!containerRef || !menuOverlayRef.current) return;

    if (isOpen) {
      openMenu();
    } else {
      closeMenu();
    }
  }, [isOpen, containerRef]);

  const openMenu = () => {
    if (isAnimating) return;
    setIsAnimating(true);

    // Arrêter Lenis pendant l'animation du menu
    if (lenisScroll) {
      lenisScroll.stop();
    }

    const tl = gsap.timeline();

    // Animation d'ouverture
    tl.to(menuToggleLabelRef.current, {
      y: "-110%",
      duration: 1,
      ease: "hop",
    }, "<")
    .to(containerRef?.current, {
      y: "100svh",
      duration: 1,
      ease: "hop",
    }, "<")
    .to(menuOverlayRef.current, {
      clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
      duration: 1,
      ease: "hop",
    }, "<")
    .to(menuOverlayContentRef.current, {
      yPercent: 0,
      duration: 1,
      ease: "hop",
    }, "<")
    .to(menuMediaWrapperRef.current, {
      opacity: 1,
      duration: 0.75,
      ease: "power2.out",
      delay: 0.5,
    }, "<");

    // Animation des textes
    splitTextByContainer.current.forEach((containerSplits) => {
      const copyLines = containerSplits.flatMap((split) => split.lines);
      tl.to(copyLines, {
        y: "0%",
        duration: 2,
        ease: "hop",
        stagger: -0.075,
      }, -0.15);
    });

    if (hamburgerIconRef.current) {
      hamburgerIconRef.current.classList.add(styles.active);
    }

    tl.call(() => {
      setIsAnimating(false);
    });
  };

  const closeMenu = () => {
    if (isAnimating) return;
    setIsAnimating(true);

    if (hamburgerIconRef.current) {
      hamburgerIconRef.current.classList.remove(styles.active);
    }
    const tl = gsap.timeline();

    tl.to(containerRef?.current, {
      y: "0svh",
      duration: 1,
      ease: "hop",
    })
    .to(menuOverlayRef.current, {
      clipPath: "polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)",
      duration: 1,
      ease: "hop",
    }, "<")
    .to(menuOverlayContentRef.current, {
      yPercent: -50,
      duration: 1,
      ease: "hop",
    }, "<")
    .to(menuToggleLabelRef.current, {
      y: "0%",
      duration: 1,
      ease: "hop",
    }, "<")
    .to(copyContainersRef.current, {
      opacity: 0.25,
      duration: 1,
      ease: "hop",
    }, "<");

    tl.call(() => {
      splitTextByContainer.current.forEach((containerSplits) => {
        const copyLines = containerSplits.flatMap((split) => split.lines);
        gsap.set(copyLines, { y: "-110%" });
      });

      gsap.set(copyContainersRef.current, { opacity: 1 });
      gsap.set(menuMediaWrapperRef.current, { opacity: 0 });

      setIsAnimating(false);

      // Redémarrer Lenis après la fermeture du menu
      if (lenisScroll) {
        lenisScroll.start();
      }
    });
  };

  // Fonction pour gérer la navigation avec transition immédiate
  const handleNavigation = (href) => {
    if (isAnimating) return;

    setIsAnimating(true);

    // Déclencher immédiatement la transition de page
    // Le ViewTransition va gérer l'animation de page
    if (typeof document !== 'undefined' && 'startViewTransition' in document) {
      // Utiliser View Transitions API
      const transition = document.startViewTransition(() => {
        router.push(href);
      });

      transition.ready.then(() => {
        // Réinitialiser le menu après la transition
        if (hamburgerIconRef.current) {
          hamburgerIconRef.current.classList.remove(styles.active);
        }

        // Réinitialiser les éléments du menu
        splitTextByContainer.current.forEach((containerSplits) => {
          const copyLines = containerSplits.flatMap((split) => split.lines);
          gsap.set(copyLines, { y: "-110%" });
        });

        gsap.set(copyContainersRef.current, { opacity: 1 });
        gsap.set(menuMediaWrapperRef.current, { opacity: 0 });
        gsap.set(menuOverlayRef.current, { clipPath: "polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)" });
        gsap.set(menuOverlayContentRef.current, { yPercent: -50 });
        gsap.set(menuToggleLabelRef.current, { y: "0%" });
        gsap.set(containerRef?.current, { y: "0svh" });

        // Redémarrer Lenis
        if (lenisScroll) {
          lenisScroll.start();
        }

        setIsAnimating(false);
      });
    } else {
      // Fallback pour navigateurs non compatibles
      router.push(href);
      setIsAnimating(false);
    }
  };

  return (
    <div className={styles.menuOverlay} ref={menuOverlayRef}>
      <div className={styles.menuOverlayContent} ref={menuOverlayContentRef}>
        <div className={styles.menuMediaWrapper} ref={menuMediaWrapperRef}>
          <img src={activeImage} alt="" />
        </div>
        <div className={styles.menuContentWrapper}>
          <div className={styles.menuContentMain}>
            <div 
              className={styles.menuCol}
              ref={(el) => copyContainersRef.current[0] = el}
            >
              {navItems.map((item, index) => (
                <div
                  className={styles.menuLink}
                  key={index}
                  onMouseEnter={() => setActiveImage(item.image)}
                  onMouseLeave={() => setActiveImage('/images/lucas-joliveau-siege-ordinateur-portable.png')}
                >
                  <button onClick={() => handleNavigation(item.href)}>
                    {item.title}
                  </button>
                </div>
              ))}
            </div>

            <div 
              className={styles.menuCol}
              ref={(el) => copyContainersRef.current[1] = el}
            >
              {tagItems.map((item, index) => (
                <div className={styles.menuTag} key={index}>
                  <button onClick={() => handleNavigation(item.href)}>
                    {item.title}
                  </button>
                </div>
              ))}
            </div>
          </div>
          <div className={styles.menuFooter}>
            <div 
              className={styles.menuCol}
              ref={(el) => copyContainersRef.current[2] = el}
              style={{ display: 'flex', flexDirection: 'column' }}
            >
              <p>{montrealTime}</p>
              <p>{t('location')}</p>
            </div>
            <div 
              className={styles.menuCol}
              ref={(el) => copyContainersRef.current[3] = el}
            >
              <AnimatedLink href="tel:+14388033053" className={styles.menuFooterLinks}>+****************</AnimatedLink>
              <AnimatedLink href="mailto:<EMAIL>" className={styles.menuFooterLinks}><EMAIL></AnimatedLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
