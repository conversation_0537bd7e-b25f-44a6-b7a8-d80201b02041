'use client';
import { useEffect, useRef, useState } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useTranslation } from '@/hooks/useTranslation';
import gsap from 'gsap';
import styles from './style.module.scss';
import MenuOverlay from './MenuOverlay';

export default function Header({ locale = 'fr' }) {
  const { t } = useTranslation('navigation');
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const menuToggleLabelRef = useRef(null);
  const hamburgerIconRef = useRef(null);
  const [containerRef, setContainerRef] = useState(null);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    // Charger CustomEase dynamiquement
    if (typeof window !== 'undefined') {
      import('gsap/CustomEase').then((module) => {
        gsap.registerPlugin(module.CustomEase);
        module.CustomEase.create("hop", ".87,0,.13,1");
      });
    }

    // Récupérer la référence du container de page avec un délai
    const getContainerRef = () => {
      if (typeof window !== 'undefined' && window.pageContainerRef) {
        setContainerRef(window.pageContainerRef);
      } else {
        // Réessayer après un court délai
        setTimeout(getContainerRef, 100);
      }
    };

    getContainerRef();
  }, []);

  // Détection du scroll pour masquer les lettres du logo en mobile
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      setIsScrolled(scrollY > 50); // Seuil de 50px
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, []);

  useEffect(() => {
    if (isMenuOpen) setIsMenuOpen(false);
  }, [pathname]);

  const toggleMenu = () => {
    if (isAnimating) return;
    console.log('Toggling menu:', !isMenuOpen);
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className={styles.nav}>
      <div className={styles.menuBar}>
        <div className={styles.menuLogo}>
          <Link href={`/${locale}`}>
            <motion.svg
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.3 }}
              width="40"
              height="13"
              viewBox="0 0 60 13"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
<svg width="60" height="13" viewBox="0 0 60 13" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.28569 6.49198L8.97831e-06 12.6738L0 7.66202L6.16305 0.454109L10.6388 0.454102L5.30196 6.47296L5.28569 6.49198ZM8.97831e-06 5.5467V0.454109H4.35413L8.97831e-06 5.5467ZM10.6388 12.9934H1.52459L6.08153 7.66202L10.6388 12.9934Z" className={`${styles.menuLogoIcon} ${isMenuOpen ? styles.active : ''}`}/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.2396 10.0082H22.8184L19.4886 5.32139L22.512 1.85693H20.8582L17.2302 6.01441V1.85693H15.9766V10.0082H17.2302V7.91183L18.5985 6.34566L21.2396 10.0082ZM26.9993 10.0082H28.1509L28.0971 8.66238V5.82325C28.0971 4.47985 27.0578 3.53281 25.6356 3.53281C24.2401 3.53281 23.1331 4.45066 23.0046 5.81113L22.993 5.9342L24.3265 6.11595L24.3305 5.97256C24.3419 5.56381 24.4831 5.23841 24.7059 5.01574C24.9286 4.79319 25.2411 4.66478 25.6113 4.66478C25.9836 4.66478 26.2972 4.78849 26.5166 4.99431C26.7354 5.19948 26.8678 5.49274 26.8678 5.84754V5.94469C26.8678 6.00609 26.8463 6.0488 26.8091 6.08092C26.7685 6.11599 26.6995 6.1461 26.5948 6.15656L26.5938 6.15667L24.5044 6.39951L24.5037 6.39958C24.0162 6.45101 23.6042 6.64452 23.3132 6.94855C23.0216 7.25328 22.8583 7.66228 22.8583 8.13062C22.8583 9.3374 23.8592 10.1053 25.0767 10.1053C25.8747 10.1053 26.5301 9.77542 26.9365 9.23661L26.9993 10.0082ZM26.5983 7.21415C26.6979 7.20245 26.7914 7.18776 26.8678 7.17103V7.40198C26.8678 8.23949 26.1872 8.87619 25.1739 8.87619C24.5313 8.87619 24.1119 8.51599 24.1119 8.08204C24.1119 7.89424 24.1734 7.7466 24.2857 7.63682C24.3999 7.52518 24.5772 7.44243 24.8251 7.40839L26.5978 7.21421L26.5983 7.21415ZM30.4524 12.437V9.18467C30.9468 9.75969 31.6407 10.1053 32.4379 10.1053C34.1718 10.1053 35.4582 8.70087 35.4582 6.79477C35.4582 4.93516 34.1698 3.53281 32.4136 3.53281C31.5715 3.53281 30.8542 3.90837 30.3633 4.52175L30.2969 3.67854H29.2231V12.437H30.4524ZM37.5212 10.0082V6.57776C37.5451 5.59712 38.0979 4.93195 38.8263 4.93195C39.1922 4.93195 39.4312 5.02284 39.5997 5.14914L39.8002 5.29943L39.831 3.82087L39.7834 3.78119C39.6899 3.70333 39.5599 3.64842 39.4163 3.61255C39.2709 3.57619 39.1023 3.5571 38.9235 3.5571C38.2668 3.5571 37.7374 3.92173 37.4143 4.48916L37.3399 3.67854H36.292V10.0082H37.5212ZM45.8041 8.20779L45.8291 8.09775L44.5263 7.61322L44.5061 7.77458C44.4187 8.47404 43.8057 8.9612 42.9814 8.9612C42.0804 8.9612 41.3958 8.30925 41.2761 7.34862H45.8933L45.904 7.2315C45.9167 7.09208 45.9167 6.9295 45.9167 6.79793V6.79786V6.79477C45.9167 4.91624 44.7182 3.53281 42.9814 3.53281C41.2239 3.53281 39.9368 4.93652 39.9368 6.84335C39.9368 8.75237 41.2502 10.1539 43.03 10.1539C44.3379 10.1539 45.5446 9.34916 45.8041 8.20779ZM54.9071 10.0082V6.57756C54.9189 6.04791 55.1029 5.59468 55.409 5.27478C55.7145 4.95536 56.1481 4.76194 56.6738 4.76194C57.4672 4.76194 58.0275 5.37353 58.0275 6.23615V10.0082H59.2568V6.18757C59.2568 4.66758 58.211 3.53281 56.771 3.53281C55.9685 3.53281 55.2242 3.85201 54.8114 4.4173L54.7512 3.67854H53.6778V10.0082H54.9071ZM32.3164 8.90048C31.2314 8.90048 30.4281 8.04603 30.4281 6.81906C30.4281 5.59324 31.2546 4.7255 32.3407 4.7255C33.4218 4.7255 34.1925 5.58785 34.1925 6.81906C34.1925 8.04711 33.4004 8.90048 32.3164 8.90048ZM44.6515 6.15592H41.2661C41.3941 5.30182 42.1086 4.70122 42.9692 4.70122C43.8313 4.70122 44.5444 5.30306 44.6515 6.15592ZM49.6259 10.1539C51.4766 10.1539 52.792 8.7545 52.792 6.84335C52.792 4.93221 51.4766 3.53281 49.6259 3.53281C47.7752 3.53281 46.4598 4.93221 46.4598 6.84335C46.4598 8.7545 47.7752 10.1539 49.6259 10.1539ZM49.6259 8.93691C48.5417 8.93691 47.7255 8.04691 47.7255 6.84335C47.7255 5.62621 48.543 4.7255 49.6259 4.7255C50.7088 4.7255 51.5263 5.62621 51.5263 6.84335C51.5263 8.04691 50.7101 8.93691 49.6259 8.93691Z" className={`${styles.menuLogoLetters} ${isMenuOpen ? styles.active : ''} ${isScrolled ? styles.scrolled : ''}`}/>
</svg>

            </motion.svg>
          </Link>
        </div>
        <div className={styles.menuToggleBtn} onClick={toggleMenu}>
          <div className={styles.menuToggleLabel}>
            <p ref={menuToggleLabelRef}>{t('menu')}</p>
          </div>
          <div
            className={`${styles.menuHamburgerIcon} ${isMenuOpen ? styles.active : ''}`}
            ref={hamburgerIconRef}
          >
            <span></span>
            <span></span>
          </div>
        </div>
      </div>

      <MenuOverlay
        isOpen={isMenuOpen}
        locale={locale}
        isAnimating={isAnimating}
        setIsAnimating={setIsAnimating}
        menuToggleLabelRef={menuToggleLabelRef}
        hamburgerIconRef={hamburgerIconRef}
        containerRef={containerRef}
      />
    </nav>
  );
}
