'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import styles from './style.module.scss';
import { useTranslation } from '@/hooks/useTranslation';

/**
 * Breadcrumb
 * @param {Array<{label:string; href:string}>} [items] – passe tes propres segments
 * @param {string} [className]
 */
export default function Breadcrumb({ items, className = '' }) {
  const { t } = useTranslation('common');
  // fallback : déduire à partir du pathname “/blog/2025-05-17-article”
  const pathname = usePathname();
  const auto = pathname
    .split('/')
    .filter(Boolean)
    .map((seg, i, arr) => ({
      label: decodeURIComponent(seg.replace(/-/g, ' ')),
      href: '/' + arr.slice(0, i + 1).join('/'),
    }));

  const list = items ?? auto;

  return (
    <nav aria-label="Fil d’ariane" className={`${styles.root} ${className} container`}>
      <ol itemScope itemType="https://schema.org/BreadcrumbList">
        {list.map((it, idx) => (
          <li
            key={it.href}
            itemProp="itemListElement"
            itemScope
            itemType="https://schema.org/ListItem"
            className={styles.item}
          >
            {idx < list.length - 1 ? (
              <Link href={it.href} itemProp="item">
                <span itemProp="name">{it.label}</span>
              </Link>
            ) : (
              <span aria-current="page" itemProp="name">
                {it.label}
              </span>
            )}
            <meta itemProp="position" content={idx + 1} />
          </li>
        ))}
      </ol>
    </nav>
  );
}
