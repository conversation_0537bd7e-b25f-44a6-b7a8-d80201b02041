@import "@/styles/colors.scss";

.item {
  color: $color-dark-brown;
}

.root {
  font-size: 0.875rem;
  ol {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0;

    li {
      display: inline-flex;
      align-items: center;

      a {
        color: $color-dark-gray;
        text-decoration: none;
        &:hover { text-decoration: underline; }
      }

      &::after {
        content: "›";
        margin: 0 0.5rem;
        color: $color-dark-gray;
      }

      &:last-child::after { content: none; }
    }
  }
}
