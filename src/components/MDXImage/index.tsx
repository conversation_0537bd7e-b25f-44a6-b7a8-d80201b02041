import Image, { type ImageProps } from 'next/image';

type Props = Omit<ImageProps, 'fill'> & {
  maxWidth?: number;          // limite facultative du contenu, ex 800 px
};

export default function MDXImage({
  width = 1200,               // valeur par défaut si l’auteur n’en donne pas
  height = 675,               // ratio 16/9 par défaut
  maxWidth = 800,
  style,
  alt = '',                   // alt vide par défaut pour les images décoratives
  ...rest
}: Props) {
  return (
    <Image
      {...rest}
      alt={alt}
      width={width}
      height={height}
      sizes="100vw"
      style={{
        display: 'block',
        width: '100%',        // ←  occupe toute la largeur de l’article
        height: 'auto',       // ←  conserve le ratio
        maxWidth,             // ←  limite : retire ‑la si tu veux 100 vw
        margin: '2rem 0',
        ...style,
      }}
    />
  );
}
