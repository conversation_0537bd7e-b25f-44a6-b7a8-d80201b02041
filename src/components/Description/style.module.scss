@import "@/styles/colors.scss";

.description {
    padding-top: var(--section-padding);
    padding-bottom: var(--section-padding);
    display: flex;
    justify-content: center;

    .title {
        color: $color-dark-brown;
    }

    .body {
        display: flex;
        flex-direction: column; // Mobile par défaut
        gap: 30px; // Réduit l'espace sur mobile
        position: relative;

        // h2 {
        //     margin: 0;
        //     gap: 8px;

        //     span {
        //         margin-right: 3px;
        //     }

        //     .mask {
        //         position: relative;
        //         overflow: hidden;
        //         display: inline-flex;
        //     }
        // }

        .descriptionText {
            width: 100%; // Largeur complète sur mobile
            color: $color-dark-brown;

            p {
                margin: 0;

                // Ajouter un espacement entre les paragraphes multiples
                &:not(:last-child) {
                    margin-bottom: 1.5rem;
                }
            }
        }

        .button {
            top: auto;
            left: auto;
            width: 140px; // Taille réduite sur mobile
            height: 140px;
            background-color: #1C1D20;
            color: white;
            border-radius: 50%;
            position: relative; // Position relative sur mobile pour éviter le débordement
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            align-self: center; // Centre le bouton sur mobile

            p {
                margin: 0;
                font-size: 14px; // Légèrement plus petit sur mobile
                font-weight: 300;
                position: relative;
                z-index: 1;
            }
        }
    }

    .moreWork {
        padding: 20px;
        border-radius: 40px;
        border: 1px solid grey;
    }

    // Ajustements pour tablettes et desktops
    @media (min-width: 768px) {
        .description {
            // Garde le centrage mais permet au grid de prendre toute la largeur
            align-items: stretch;
            justify-content: none;
        }

        .body {
            display: grid;
            grid-template-columns: 1fr 1.5fr; // 40% / 60% ratio
            gap: var(--gap-padding);
            max-width: 1400px;
            align-items: start;
            width: 100%; // Prend toute la largeur du container centré
        }

        .descriptionText {
            p {
                margin-top: 0.5rem;

                // Maintenir l'espacement entre paragraphes sur desktop
                &:not(:last-child) {
                    margin-bottom: 1.5rem;
                }
            }
        }

        .button {
            position: absolute; // Reprend sa position absolue sur desktop
            top: 80%;
            left: calc(100% - 200px);
            width: 180px;
            height: 180px;
        }
    }
}
