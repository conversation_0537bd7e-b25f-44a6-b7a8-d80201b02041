import Masonry from "react-masonry-css";
import MediaDisplay from "@/components/MediaDisplay";
import styles from "./style.module.scss";

export default function MyMasonry({ images, columns }) {
  const breakpointColumnsObj = {
    default: columns,
    1100: columns,
    700: 1,
  };

  return (
    <Masonry
      breakpointCols={breakpointColumnsObj}
      className={`${styles.myMasonryGrid} masonryGridGlobal`}
      columnClassName={`${styles.myMasonryGridColumn} masonryGridColumnGlobal`}
    >
      {images.map((image, i) => (
        <div key={i}>
          <MediaDisplay
            src={image}
            alt=""
            width={800}
            height={600}
          />
        </div>
      ))}
    </Masonry>
  );
}
