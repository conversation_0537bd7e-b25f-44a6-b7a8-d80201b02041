"use client";

import styles from "./page.module.scss";
import Hero from "@/components/Heros/Hero";
import { useTranslation } from "@/hooks/useTranslation";

export default function Home1Client({ params }) {
  const { t } = useTranslation('home');
  const locale = params.locale || 'fr';

  return (
    <section>
      <Hero
        title='Designers et développeurs pour entreprises québécoises'
        locale={locale}
        containerClass={`container ${styles.hero2}`}
      />
    </section>
  );
}
