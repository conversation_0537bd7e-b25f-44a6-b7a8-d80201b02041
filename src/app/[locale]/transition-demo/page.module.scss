// Styles pour la page de démonstration des transitions
// Inspiré de transition_source/main.css

.main {
  width: 100vw;
  min-height: 100vh;
  background-color: #f1efe7;
  color: #242726;
}

.nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  background: rgba(241, 239, 231, 0.9);
  backdrop-filter: blur(10px);

  a {
    display: inline-block;
    position: relative;
    text-decoration: none;
    color: #242726;
    font-size: 14px;
    font-weight: 600;
    padding: 0.5rem 1rem;
    transition: color 0.3s ease;

    &:hover {
      color: #666;
    }
  }
}

.container {
  width: 100%;
  padding-top: 80px; // Pour compenser la nav fixe
}

.hero {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;

  h1 {
    font-size: clamp(4rem, 15vw, 20rem);
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: -0.5rem;
    line-height: 1;
    text-align: center;
  }
}

.images {
  width: 100%;
  background-color: #f1efe7;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 8rem 2rem;

  img {
    width: 60%;
    max-width: 600px;
    margin: 0 auto;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
}

.info {
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 4rem 2rem;
  gap: 4rem;

  .col {
    flex: 1;

    &:first-child {
      img {
        width: 100%;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      }
    }

    &:last-child {
      display: flex;
      flex-direction: column;
      justify-content: center;

      p {
        font-weight: 600;
        font-size: 2rem;
        line-height: 1.4;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
    }
  }
}

.testLinks {
  padding: 4rem 2rem;
  text-align: center;

  h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    font-weight: 700;
  }
}

.linkGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;

  a {
    display: block;
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    text-decoration: none;
    color: #242726;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .nav {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .hero h1 {
    font-size: 12vw;
  }

  .info {
    flex-direction: column;
    gap: 2rem;

    .col:last-child p {
      font-size: 1.5rem;
    }
  }

  .images {
    padding: 4rem 1rem;

    img {
      width: 90%;
    }
  }

  .linkGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
