"use client";

import Hero4 from '@/components/Heros/Hero4';
import Hero3 from '@/components/Heros/Hero3';
import StatsCards from '@/components/StatsCards';
import ThreeColumnTitle from '@/components/ThreeColumnTitle';
import ExpertiseAccordion from '@/components/ExpertiseAccordion';
import Testimonials from '@/components/Testimonials';
import Values from '@/components/Values';
import TitleTextImages from '@/components/TitleTextImages';
import Description from '@/components/Description';
import BasicCenterCTA from '@/components/CallToAction/BasicCenterCTA';
import { useTranslation } from '@/hooks/useTranslation';


export default function AgencyClient({ params }) {
  const locale = params.locale || 'fr';
  const { t } = useTranslation('agency');



  return (
    <div>
      <Hero4
        title={t('hero4.title')}
        subtitle={t('hero4.subtitle')}
        description={t('hero4.description')}
        imageAlt={t('hero4.image_alt')}
        imgSrc="/images/lucas-joliveau-siege-ordinateur-portable.png"
        locale={locale}
      />
      <div className="container">
              <Hero3
                subtitle={t('hero3.subtitle')}
                title={t('hero3.title')}
                description={t('hero3.description')}
                buttonText={t('hero3.button.text')}
                buttonLink="/"
                locale={locale}
              />
              </div>
            <StatsCards />
      <TitleTextImages
        title={t('titletextimages.title')}
        description={t('titletextimages.description')}
        descriptionHedhofisLinkText={t('titletextimages.description_hedhofis_link_text')}
        image1Alt={t('titletextimages.image1_alt')}
        image2Alt={t('titletextimages.image2_alt')}
      />
      <Description
        descriptionTitle={t('descriptiontext.title')}
        descriptionText={t('descriptiontext.text')}
        showButton={false}
        titleTag="h3"
      />


      <ThreeColumnTitle
        locale={locale}
        title="three_column_title.title"
        description="three_column_title.description"
        image1Alt="three_column_title.image1_alt"
        image2Alt="three_column_title.image2_alt"
        image3Alt="three_column_title.image3_alt"
      />
      <Values
        value1Title={t('values1.title')}
        value1Text="values1.text"
        value2Title="values2.title"
        value2Text="values2.text"
        value3Title="values3.title"
        value3Text="values3.text"
      />
      <Testimonials
        locale={locale}
        sectionLabel="testimonials.section_label"
        sectionTitle="testimonials.section_title"
        sectionDescription="testimonials.section_description"
        sectionDescriptionLinkText="testimonials.section_description_link_text"
        viewAllButton="testimonials.view_all_button"
        testimonialsData="testimonials.testimonials_data"
        dragText="testimonials.drag_text"
      />
      <ExpertiseAccordion
        title="expertises.title"
        expertisesData="expertises.items"
        cursorText="expertises.cursor_text"
      />
    <BasicCenterCTA
      backgroundColor="#E2E3DD"
      titleColor="#120900"
      textColor="#120900"
      title={t('basiccentercta.title')}
      text={t('basiccentercta.text')}
      buttonText={t('basiccentercta.button.text')}
      buttonHref={`/${locale}/contact`}
      buttonVariant='dark'
    />
    </div>
  );
}

