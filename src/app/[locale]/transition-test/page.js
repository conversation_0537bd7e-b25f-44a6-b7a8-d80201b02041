import TransitionCopy from '@/components/TransitionCopy';
import Link from 'next/link';

export default function TransitionTest({ params }) {
  const locale = params.locale || 'fr';
  
  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '2rem',
      backgroundColor: '#e3e4d8',
      gap: '2rem'
    }}>
      <TransitionCopy delay={0.3}>
        <h1 style={{
          fontSize: 'clamp(3rem, 8vw, 8rem)',
          fontWeight: 800,
          lineHeight: 1,
          textTransform: 'uppercase',
          color: '#141414',
          textAlign: 'center',
          margin: 0
        }}>
          Page Transition Test
        </h1>
      </TransitionCopy>
      
      <TransitionCopy delay={0.6}>
        <p style={{
          fontSize: '1.2rem',
          color: '#141414',
          textAlign: 'center',
          maxWidth: '600px',
          margin: 0
        }}>
          Cette page démontre le système de transition entre les pages. 
          Cliquez sur les liens ci-dessous pour voir les animations en action.
        </p>
      </TransitionCopy>
      
      <div style={{
        display: 'flex',
        gap: '2rem',
        flexWrap: 'wrap',
        justifyContent: 'center'
      }}>
        <Link 
          href={`/${locale}`}
          style={{
            padding: '1rem 2rem',
            backgroundColor: '#222',
            color: '#e3e4d8',
            textDecoration: 'none',
            textTransform: 'uppercase',
            fontWeight: 500,
            fontSize: '0.9rem',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          Retour à l'accueil
        </Link>
        
        <Link 
          href={`/${locale}/contact`}
          style={{
            padding: '1rem 2rem',
            backgroundColor: '#222',
            color: '#e3e4d8',
            textDecoration: 'none',
            textTransform: 'uppercase',
            fontWeight: 500,
            fontSize: '0.9rem',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          Page Contact
        </Link>
        
        <Link 
          href={`/${locale}/projets`}
          style={{
            padding: '1rem 2rem',
            backgroundColor: '#222',
            color: '#e3e4d8',
            textDecoration: 'none',
            textTransform: 'uppercase',
            fontWeight: 500,
            fontSize: '0.9rem',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          Projets
        </Link>
      </div>
    </div>
  );
}
