@import "@/styles/colors.scss";

.grid {
  display: grid;
  gap: 5rem 1rem;
  /* 5 colonnes : small = 2fr (40%), big = 3fr (60%) */
  grid-template-columns: repeat(5, 1fr);
  padding-bottom: var(--section-padding);
}

@media (max-width: 1024px) {
  .grid { grid-template-columns: repeat(2, 1fr); }
}
@media (max-width: 640px) {
  .grid { grid-template-columns: 1fr; }
}

/* ── cartes ── */
.card {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  text-decoration: none;
  color: inherit;
}

.blogLink {
      text-decoration: none;

}

/* small = 2/5, big = 3/5 */
.small { grid-column: span 2; }
.big   { grid-column: span 3; }

/* wrapper pour Image fill */
.imageWrapper {
  position: relative;
  width: 100%;
}

/* même hauteur partout via aspect-ratio calculé :
   - small : carré → 1/1
   - big   : ratio = 3/2 pour que son height == small width */
.small .imageWrapper { aspect-ratio: 1 / 1; }
.big   .imageWrapper { aspect-ratio: 3 / 2; }

.image {
  object-fit: cover;
}

/* mobile : toutes carrées */
@media (max-width: 640px) {
  .small .imageWrapper,
  .big   .imageWrapper {
    aspect-ratio: 3 / 2 !important;
  }
}

.title {
    margin-bottom: 0;
    color: $color-dark-brown;
}

.category {
    margin-top: 0;
    color: $color-dark-gray;
}
